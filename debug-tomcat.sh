#!/bin/bash
# Tomcat 启动问题诊断脚本

IMAGE_NAME="192.168.200.39:1443/btit/infra/tomcat:9.0.100-jdk8-btit-jammy"

echo "=== Tomcat 启动问题诊断 ==="
echo "镜像: $IMAGE_NAME"
echo ""

echo "1. 检查环境变量..."
docker run --rm --entrypoint bash "$IMAGE_NAME" -c "
echo 'CATALINA_HOME: $CATALINA_HOME'
echo 'CATALINA_BASE: $CATALINA_BASE'
echo 'PATH: $PATH'
echo 'PWD: $(pwd)'
echo 'USER: $(whoami)'
echo 'UID: $(id -u)'
echo 'GID: $(id -g)'
"

echo ""
echo "2. 检查关键文件权限..."
docker run --rm --entrypoint bash "$IMAGE_NAME" -c "
echo '=== /usr/local/tomcat/bin/ 目录内容 ==='
ls -la /usr/local/tomcat/bin/ | head -10

echo ''
echo '=== setclasspath.sh 文件详情 ==='
ls -la /usr/local/tomcat/bin/setclasspath.sh

echo ''
echo '=== catalina.sh 文件详情 ==='
ls -la /usr/local/tomcat/bin/catalina.sh

echo ''
echo '=== 检查文件是否可读 ==='
if [ -r /usr/local/tomcat/bin/setclasspath.sh ]; then
    echo 'setclasspath.sh 可读: YES'
else
    echo 'setclasspath.sh 可读: NO'
fi

if [ -x /usr/local/tomcat/bin/setclasspath.sh ]; then
    echo 'setclasspath.sh 可执行: YES'
else
    echo 'setclasspath.sh 可执行: NO'
fi
"

echo ""
echo "3. 检查 catalina.sh 脚本内容（查找 setclasspath.sh 相关代码）..."
docker run --rm --entrypoint bash "$IMAGE_NAME" -c "
echo '=== catalina.sh 中查找 setclasspath.sh 的代码 ==='
grep -n 'setclasspath' /usr/local/tomcat/bin/catalina.sh | head -5
"

echo ""
echo "4. 尝试手动执行 setclasspath.sh..."
docker run --rm --entrypoint bash "$IMAGE_NAME" -c "
echo '=== 尝试直接执行 setclasspath.sh ==='
cd /usr/local/tomcat/bin
if ./setclasspath.sh; then
    echo 'setclasspath.sh 执行成功'
else
    echo 'setclasspath.sh 执行失败，退出码: $?'
fi
"

echo ""
echo "5. 检查 catalina.sh 的执行过程..."
docker run --rm --entrypoint bash "$IMAGE_NAME" -c "
echo '=== 使用 bash -x 调试 catalina.sh ==='
cd /usr/local/tomcat/bin
timeout 10s bash -x ./catalina.sh run 2>&1 | head -20
"

echo ""
echo "6. 检查工作目录和相对路径..."
docker run --rm --entrypoint bash "$IMAGE_NAME" -c "
echo '=== 检查不同目录下的文件访问 ==='
echo 'Current directory: $(pwd)'
echo 'Files in current directory:'
ls -la . | head -5

echo ''
echo 'Changing to /usr/local/tomcat:'
cd /usr/local/tomcat
echo 'Current directory: $(pwd)'
echo 'bin/setclasspath.sh exists: $(test -f bin/setclasspath.sh && echo YES || echo NO)'

echo ''
echo 'Changing to /usr/local/tomcat/bin:'
cd /usr/local/tomcat/bin
echo 'Current directory: $(pwd)'
echo 'setclasspath.sh exists: $(test -f setclasspath.sh && echo YES || echo NO)'
echo './setclasspath.sh exists: $(test -f ./setclasspath.sh && echo YES || echo NO)'
"

echo ""
echo "=== 诊断完成 ==="
