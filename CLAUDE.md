# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个医疗产品离线容器化部署方案，支持云密钥系统、电子签章系统、快捷签系统等医疗相关产品在隔离网络环境下的自动化部署。项目采用 Docker 容器化技术，支持 x86_64 和 aarch64 双架构部署。

## 系统架构

### 三层架构设计
1. **基础设施层** (`infrastructure/`)
   - MySQL 数据库集群（主从复制）
   - Redis 缓存服务（主从架构）
   - Nginx 网关服务（负载均衡）
   - Keepalived 高可用保障

2. **业务应用层** (`products/`)
   - 云密钥系统（CloudKey）
   - 电子签章系统（ESeal）
   - 快捷签系统（QuickSign）

3. **运维管控层** (`operations/`)
   - 日志管理（ELK + logrotate）
   - 高可用配置（故障转移）
   - 备份管理（数据备份恢复）

## 常用开发命令

### 镜像构建
```bash
# 构建 Tomcat 镜像（支持多版本）
./docker/scripts/build/build-tomcat.sh -j 8 -t 9.0.100 -v crypto -a amd64

# 构建 JDK 镜像
./docker/scripts/build/build-jdk.sh -j 8 -a amd64

# 构建所有操作系统镜像
./docker/scripts/build/build-all-os.sh
```

### 部署和运维
```bash
# 启动镜像仓库服务
./docker/scripts/deploy/start-registry.sh

# 加载镜像到本地仓库
./docker/scripts/deploy/load-images.sh

# 启动所有服务
./docker/scripts/deploy/start-services.sh

# MySQL 部署
./infrastructure/database/mysql/scripts/deploy.sh

# Redis 部署
./infrastructure/database/redis/scripts/deploy-redis.sh
```

### 服务管理
```bash
# CloudKey 服务控制
./products/cloudkey/scripts/cloudkey-start.sh
./products/cloudkey/scripts/cloudkey-stop.sh
./products/cloudkey/scripts/cloudkey-status.sh

# Nginx 服务控制
./infrastructure/gateway/nginx/scripts/nginx-control.sh

# Keepalived 服务控制
./infrastructure/gateway/keepalived/scripts/keepalived-service-ctl.sh
```

## 关键配置文件

### Harbor 镜像仓库配置
- `docker/scripts/config/build-tomcat.conf` - Tomcat 构建配置
- `infrastructure/registry/config/config.yml` - Registry 服务配置

### 服务编排配置
- `products/cloudkey/docker-compose.yml` - CloudKey 服务编排
- `infrastructure/gateway/nginx/docker-compose.yml` - Nginx 服务编排
- `infrastructure/registry/docker-compose.yml` - Registry 服务编排

### 版本和镜像管理
- `docker/scripts/build/versions/` - 各组件版本配置文件
- `registry/images/` - 预打包镜像仓库（按架构分类）

## 重要技术特性

### OpenEuler 容器兼容性
在 OpenEuler 环境下运行 Java 容器时需要特殊配置：
```yaml
security_opt:
  - seccomp=unconfined
  - apparmor=unconfined
cap_add:
  - SYS_ADMIN
```

### 加密库支持
项目支持国产加密库，包括：
- Kylin 操作系统加密库（`docker/infrastructure/os/shared/crypto-files/kylin/`）
- 通用加密库（`docker/infrastructure/tomcat/shared/crypto-files/`）

### 离线部署机制
- 完整的离线安装包（`setup/`）
- 预打包镜像仓库（`registry/images/`）
- 自动化部署脚本（`docker/scripts/deploy/`）

## 项目规则

### Git 提交规范
- 格式：`<type>(<scope>): <subject>`
- 类型：feat|fix|docs|style|refactor|test|chore
- 示例：`feat(tomcat): 添加健康检查功能`

### 目录结构管理
- 新增目录必须添加 README.md 说明文件
- 目录结构变更必须同步更新 `dir-structure.txt`
- 配置文件必须放在对应服务的 config 目录下

### 安全要求
- 敏感配置使用环境变量管理
- 所有容器必须配置安全选项
- 加密相关设备需要特殊权限配置

## 故障排查

### 常见问题
1. **OpenEuler JVM 启动失败**：检查安全配置是否正确
2. **加密设备访问失败**：确认设备映射和权限配置
3. **镜像拉取失败**：检查 Harbor 仓库配置和网络连接

### 日志位置
- 应用日志：`products/*/logs/`
- 中间件日志：`infrastructure/*/logs/`
- 构建日志：`docker/scripts/build/logs/`

## 开发注意事项

1. **双架构兼容**：所有镜像构建必须支持 x86_64 和 aarch64
2. **离线优先**：所有操作都应支持离线环境
3. **配置管理**：使用模板和配置文件驱动，避免硬编码
4. **错误处理**：所有脚本必须包含完善的错误处理和日志记录
5. **版本控制**：镜像和配置文件必须包含版本信息